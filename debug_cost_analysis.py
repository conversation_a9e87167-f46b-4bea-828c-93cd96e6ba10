import sys
import os
import importlib

# 添加当前目录到路径
sys.path.append(os.getcwd())

def analyze_cost_differences():
    """深入分析两个实现的成本差异"""
    print("="*80)
    print("深入成本差异分析")
    print("="*80)
    
    try:
        # 重新加载模块
        rs5_module = importlib.import_module("Robust SOCDA 5")
        rh2_module = importlib.import_module("Robust Hybrid 2")
        
        # 创建相同的mock优化器
        class MockOptimizer:
            def __init__(self):
                self.commission_rate = 0.18
                self.travel_cost_per_unit = 0.2  
                self.penalty_cost_per_unit = 1.0
                self.driver_speed = 1.0
                self.max_orders_per_driver = 4
                self.num_drivers = 2
                self.stps = list(range(6))
                self.stp_interval = 10
                self.slot_duration = 30
                self.restaurant_coords = {0: (2, 2), 1: (8, 2)}
                self.driver_start_coords = {0: (5, 1), 1: (6, 1)}
                self.orders_data = {
                    0: {'value': 25, 'restaurant_id': 0, 'customer_coord': (1, 5), 'est_delivery': 15, 'stp': 0},
                    1: {'value': 30, 'restaurant_id': 1, 'customer_coord': (9, 4), 'est_delivery': 12, 'stp': 0},
                    2: {'value': 28, 'restaurant_id': 0, 'customer_coord': (3, 6), 'est_delivery': 18, 'stp': 0},
                }

        class MockOptimizer2:
            def __init__(self):
                self.travel_cost_per_unit = 0.2
                self.penalty_cost_per_unit = 1.0
                self.driver_speed = 1.0
                self.max_orders_per_driver = 4
                self.num_drivers = 2
                self.stps = list(range(6))
                self.stp_interval = 10
                self.commission_rate = 0.18
                self.theta_t = 0.3
                self.travel_time_deviation_ratio = 0.2
                self.restaurant_coords = {0: (2, 2), 1: (8, 2)}
                self.driver_start_coords = {0: (5, 1), 1: (6, 1)}
                self.orders_data = {
                    0: {'value': 25, 'restaurant_id': 0, 'customer_coord': (1, 5), 'est_delivery': 15, 'stp': 0},
                    1: {'value': 30, 'restaurant_id': 1, 'customer_coord': (9, 4), 'est_delivery': 12, 'stp': 0},
                    2: {'value': 28, 'restaurant_id': 0, 'customer_coord': (3, 6), 'est_delivery': 18, 'stp': 0},
                }

        mock_opt1 = MockOptimizer()
        mock_opt2 = MockOptimizer2()
        
        # 设置delta=1
        rs5_module.RobustOGGM.__init__ = lambda self, food_delivery_optimizer: rs5_module.RobustOGGM.__init__.__wrapped__(self, food_delivery_optimizer) if hasattr(rs5_module.RobustOGGM.__init__, '__wrapped__') else None
        rh2_module.OGGM.__init__ = lambda self, food_delivery_optimizer: rh2_module.OGGM.__init__.__wrapped__(self, food_delivery_optimizer) if hasattr(rh2_module.OGGM.__init__, '__wrapped__') else None
        
        # 分析STP 0的订单组生成
        print("\n1. 分析STP 0的订单组生成差异")
        print("-" * 50)
        
        # RS5的订单组生成
        robust_oggm = rs5_module.RobustOGGM(mock_opt1)
        robust_oggm.delta_for_virtual_driver = 1
        
        driver_states = {
            0: {'pos': (5, 1), 'time': 0.0},
            1: {'pos': (6, 1), 'time': 0.0}
        }
        
        rs5_groups = robust_oggm.generate_robust_order_groups_for_stp([0, 1, 2], 0, driver_states)
        print(f"RS5生成了{len(rs5_groups)}个订单组:")
        for i, og in enumerate(rs5_groups):
            print(f"  组{i}: 订单{og.orders}, 成本{og.robust_cost:.3f}")
        
        # RH2的订单组生成
        oggm = rh2_module.OGGM(mock_opt2)
        oggm.delta_for_virtual_driver = 1
        
        rh2_groups = oggm.generate_order_groups_for_stp([0, 1, 2], 0, driver_states)
        print(f"\nRH2生成了{len(rh2_groups)}个订单组:")
        for i, og in enumerate(rh2_groups):
            print(f"  组{i}: 订单{og.orders}, 成本{og.robust_cost:.3f}, 虚拟时间{og.virtual_start_time:.3f}, override{og.override_first_arc_time:.3f}")
        
        # 分析单个订单的成本计算差异
        print("\n2. 分析单个订单成本计算差异")
        print("-" * 50)
        
        # 测试订单0的单独成本
        sequence_0 = ["pickup_0", "delivery_0"]
        
        # RS5计算
        rs5_cost = robust_oggm.calculate_sequence_robust_cost_budget_constrained(
            sequence_0, None, 0.0, 0, override_first_arc_time=2.236)  # 使用典型的override时间
        print(f"RS5单独订单0成本: {rs5_cost:.3f}")
        
        # RH2计算
        rh2_cost = oggm.calculate_sequence_robust_cost(
            sequence_0, None, 0.0, 0, override_first_arc_time=2.236)
        print(f"RH2单独订单0成本: {rh2_cost:.3f}")
        
        # 分析成本计算函数的差异
        print("\n3. 分析成本计算函数差异")
        print("-" * 50)
        
        # 直接调用成本计算函数
        rs5_cost_direct = rs5_module.compute_rg2_robust_cost(
            sequence_0, None, 0.0, -1,
            mock_opt1.restaurant_coords, {0: mock_opt1.orders_data[0]['customer_coord']}, None,
            mock_opt1.driver_speed, mock_opt1.slot_duration,
            0.3, 0.2,
            mock_opt1.travel_cost_per_unit, mock_opt1.penalty_cost_per_unit,
            mock_opt1.orders_data[0]['est_delivery'], mock_opt1.driver_start_coords[0],
            mock_opt1.orders_data, mock_opt1.stp_interval, 0,
            override_first_arc_time=2.236
        )
        print(f"RS5直接成本计算: 旅行{rs5_cost_direct[0]:.3f}, 延迟{rs5_cost_direct[1]:.3f}, 总计{rs5_cost_direct[2]:.3f}")
        
        rh2_cost_direct = rh2_module.compute_rg2_robust_cost(
            sequence_0, None, 0.0, 0,
            mock_opt2.restaurant_coords, None, mock_opt2.driver_speed,
            mock_opt2.theta_t, mock_opt2.travel_time_deviation_ratio,
            mock_opt2.travel_cost_per_unit, mock_opt2.penalty_cost_per_unit,
            mock_opt2.orders_data, mock_opt2.stp_interval, override_first_arc_time=2.236
        )
        print(f"RH2直接成本计算: 旅行{rh2_cost_direct[0]:.3f}, 延迟{rh2_cost_direct[1]:.3f}, 总计{rh2_cost_direct[2]:.3f}")
        
        print("\n4. 关键差异总结")
        print("-" * 50)
        print("如果成本计算函数相同，但总成本差异巨大，问题可能在于：")
        print("1. 订单组选择策略不同")
        print("2. 司机分配策略不同") 
        print("3. 时间约束处理不同")
        print("4. 虚拟司机信息使用不一致")
        
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_cost_differences()
