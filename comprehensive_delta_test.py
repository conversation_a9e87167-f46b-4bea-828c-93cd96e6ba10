import sys
import os
import importlib

# 添加当前目录到路径
sys.path.append(os.getcwd())

def reload_modules():
    """重新加载模块以确保获取最新的代码"""
    modules_to_reload = ["Robust SOCDA 5", "Robust Hybrid 2"]
    for module_name in modules_to_reload:
        if module_name in sys.modules:
            importlib.reload(sys.modules[module_name])

def test_rs5_complete_with_delta(delta_value):
    """测试RS5完整流程"""
    print(f"\n{'='*60}")
    print(f"测试 Robust SOCDA 5.py 完整流程 (delta={delta_value})")
    print(f"{'='*60}")
    
    try:
        # 重新加载模块
        reload_modules()
        rs5_module = importlib.import_module("Robust SOCDA 5")
        
        # 创建mock优化器
        class MockOptimizer:
            def __init__(self):
                self.commission_rate = 0.18
                self.travel_cost_per_unit = 0.2  
                self.penalty_cost_per_unit = 1.0
                self.driver_speed = 1.0
                self.max_orders_per_driver = 4
                self.num_drivers = 2
                self.stps = list(range(6))
                self.stp_interval = 10
                self.slot_duration = 30
                self.restaurant_coords = {0: (2, 2), 1: (8, 2)}
                self.driver_start_coords = {0: (5, 1), 1: (6, 1)}
                self.orders_data = {
                    0: {'value': 25, 'restaurant_id': 0, 'customer_coord': (1, 5), 'est_delivery': 15, 'stp': 0},
                    1: {'value': 30, 'restaurant_id': 1, 'customer_coord': (9, 4), 'est_delivery': 12, 'stp': 0},
                    2: {'value': 28, 'restaurant_id': 0, 'customer_coord': (3, 6), 'est_delivery': 18, 'stp': 0},
                    3: {'value': 32, 'restaurant_id': 1, 'customer_coord': (7, 5), 'est_delivery': 14, 'stp': 1},
                    4: {'value': 26, 'restaurant_id': 0, 'customer_coord': (2, 7), 'est_delivery': 16, 'stp': 1},
                    5: {'value': 35, 'restaurant_id': 1, 'customer_coord': (8, 6), 'est_delivery': 13, 'stp': 1},
                    6: {'value': 29, 'restaurant_id': 0, 'customer_coord': (4, 5), 'est_delivery': 15, 'stp': 2},
                    7: {'value': 33, 'restaurant_id': 1, 'customer_coord': (6, 7), 'est_delivery': 17, 'stp': 2},
                    8: {'value': 27, 'restaurant_id': 0, 'customer_coord': (1, 8), 'est_delivery': 20, 'stp': 2},
                    9: {'value': 31, 'restaurant_id': 1, 'customer_coord': (9, 7), 'est_delivery': 16, 'stp': 3},
                    10: {'value': 24, 'restaurant_id': 0, 'customer_coord': (3, 4), 'est_delivery': 14, 'stp': 3},
                    11: {'value': 36, 'restaurant_id': 1, 'customer_coord': (7, 8), 'est_delivery': 18, 'stp': 3},
                    12: {'value': 28, 'restaurant_id': 0, 'customer_coord': (2, 6), 'est_delivery': 15, 'stp': 4},
                    13: {'value': 34, 'restaurant_id': 1, 'customer_coord': (8, 5), 'est_delivery': 13, 'stp': 4},
                    14: {'value': 30, 'restaurant_id': 0, 'customer_coord': (4, 7), 'est_delivery': 17, 'stp': 4},
                    15: {'value': 32, 'restaurant_id': 1, 'customer_coord': (6, 8), 'est_delivery': 16, 'stp': 5},
                    16: {'value': 26, 'restaurant_id': 0, 'customer_coord': (1, 6), 'est_delivery': 18, 'stp': 5},
                    17: {'value': 37, 'restaurant_id': 1, 'customer_coord': (9, 6), 'est_delivery': 14, 'stp': 5}
                }

        mock_optimizer = MockOptimizer()
        
        # 手动设置delta值
        original_init = rs5_module.RobustOGGM.__init__
        def modified_init(self, food_delivery_optimizer):
            original_init(self, food_delivery_optimizer)
            self.delta_for_virtual_driver = delta_value
        rs5_module.RobustOGGM.__init__ = modified_init
        
        # 运行完整测试
        robust_socda = rs5_module.RobustSOCDA(mock_optimizer)
        
        # 手动运行一个简化的测试流程
        driver_states = {
            k: {'pos': mock_optimizer.driver_start_coords[k], 'time': 0.0}
            for k in range(mock_optimizer.num_drivers)
        }
        
        total_travel = 0.0
        total_penalty = 0.0
        
        for stp in mock_optimizer.stps:
            stp_orders = [oid for oid, data in mock_optimizer.orders_data.items() if data['stp'] == stp]
            
            # 更新司机时间
            for k in driver_states:
                driver_states[k]['time'] = max(driver_states[k]['time'], stp * mock_optimizer.stp_interval)

            travel, penalty, final_drivers, _ = robust_socda.solve_single_stp_robust_dispatch(
                stp_orders, stp, driver_states)
            
            total_travel += travel
            total_penalty += penalty
            
            # 更新司机状态
            for driver in final_drivers:
                driver_states[driver.driver_id] = {'pos': driver.current_pos, 'time': driver.current_time}
        
        total_cost = total_travel + total_penalty
        
        # 恢复原始初始化函数
        rs5_module.RobustOGGM.__init__ = original_init
        
        print(f"RS5 delta={delta_value} 结果:")
        print(f"  旅行成本: {total_travel:.2f}")
        print(f"  延迟成本: {total_penalty:.2f}")
        print(f"  总成本: {total_cost:.2f}")
        
        return total_cost, total_travel, total_penalty
        
    except Exception as e:
        print(f"RS5 delta={delta_value} 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def test_rh2_complete_with_delta(delta_value):
    """测试RH2完整流程"""
    print(f"\n{'='*60}")
    print(f"测试 Robust Hybrid 2.py 完整流程 (delta={delta_value})")
    print(f"{'='*60}")
    
    try:
        # 重新加载模块
        reload_modules()
        rh2_module = importlib.import_module("Robust Hybrid 2")
        
        class MockOptimizer:
            def __init__(self):
                self.travel_cost_per_unit = 0.2
                self.penalty_cost_per_unit = 1.0
                self.driver_speed = 1.0
                self.max_orders_per_driver = 4
                self.num_drivers = 2
                self.stps = list(range(6))
                self.stp_interval = 10
                self.commission_rate = 0.18
                self.theta_t = 0.3
                self.travel_time_deviation_ratio = 0.2
                self.restaurant_coords = {0: (2, 2), 1: (8, 2)}
                self.driver_start_coords = {0: (5, 1), 1: (6, 1)}
                self.orders_data = {
                    0: {'value': 25, 'restaurant_id': 0, 'customer_coord': (1, 5), 'est_delivery': 15, 'stp': 0},
                    1: {'value': 30, 'restaurant_id': 1, 'customer_coord': (9, 4), 'est_delivery': 12, 'stp': 0},
                    2: {'value': 28, 'restaurant_id': 0, 'customer_coord': (3, 6), 'est_delivery': 18, 'stp': 0},
                    3: {'value': 32, 'restaurant_id': 1, 'customer_coord': (7, 5), 'est_delivery': 14, 'stp': 1},
                    4: {'value': 26, 'restaurant_id': 0, 'customer_coord': (2, 7), 'est_delivery': 16, 'stp': 1},
                    5: {'value': 35, 'restaurant_id': 1, 'customer_coord': (8, 6), 'est_delivery': 13, 'stp': 1},
                    6: {'value': 29, 'restaurant_id': 0, 'customer_coord': (4, 5), 'est_delivery': 15, 'stp': 2},
                    7: {'value': 33, 'restaurant_id': 1, 'customer_coord': (6, 7), 'est_delivery': 17, 'stp': 2},
                    8: {'value': 27, 'restaurant_id': 0, 'customer_coord': (1, 8), 'est_delivery': 20, 'stp': 2},
                    9: {'value': 31, 'restaurant_id': 1, 'customer_coord': (9, 7), 'est_delivery': 16, 'stp': 3},
                    10: {'value': 24, 'restaurant_id': 0, 'customer_coord': (3, 4), 'est_delivery': 14, 'stp': 3},
                    11: {'value': 36, 'restaurant_id': 1, 'customer_coord': (7, 8), 'est_delivery': 18, 'stp': 3},
                    12: {'value': 28, 'restaurant_id': 0, 'customer_coord': (2, 6), 'est_delivery': 15, 'stp': 4},
                    13: {'value': 34, 'restaurant_id': 1, 'customer_coord': (8, 5), 'est_delivery': 13, 'stp': 4},
                    14: {'value': 30, 'restaurant_id': 0, 'customer_coord': (4, 7), 'est_delivery': 17, 'stp': 4},
                    15: {'value': 32, 'restaurant_id': 1, 'customer_coord': (6, 8), 'est_delivery': 16, 'stp': 5},
                    16: {'value': 26, 'restaurant_id': 0, 'customer_coord': (1, 6), 'est_delivery': 18, 'stp': 5},
                    17: {'value': 37, 'restaurant_id': 1, 'customer_coord': (9, 6), 'est_delivery': 14, 'stp': 5}
                }

        mock_optimizer = MockOptimizer()
        
        # 手动设置delta值
        original_init = rh2_module.OGGM.__init__
        def modified_init(self, food_delivery_optimizer):
            original_init(self, food_delivery_optimizer)
            self.delta_for_virtual_driver = delta_value
        rh2_module.OGGM.__init__ = modified_init
        
        # 运行完整测试
        hybrid_solver = rh2_module.HybridSolver(mock_optimizer)
        
        driver_states = {
            k: {'pos': mock_optimizer.driver_start_coords[k], 'time': 0.0}
            for k in range(mock_optimizer.num_drivers)
        }
        
        total_travel_cost = 0.0
        total_penalty_cost = 0.0

        for stp in mock_optimizer.stps:
            stp_orders = [oid for oid, data in mock_optimizer.orders_data.items() if data['stp'] == stp]
            
            travel, penalty, final_drivers, _ = hybrid_solver.solve_single_stp_dispatch(
                stp_orders, stp, driver_states)
            
            total_travel_cost += travel
            total_penalty_cost += penalty
            
            for driver in final_drivers:
                driver_states[driver.driver_id] = {'pos': driver.current_pos, 'time': driver.current_time}
        
        total_cost = total_travel_cost + total_penalty_cost
        
        # 恢复原始初始化函数
        rh2_module.OGGM.__init__ = original_init
        
        print(f"RH2 delta={delta_value} 结果:")
        print(f"  旅行成本: {total_travel_cost:.2f}")
        print(f"  延迟成本: {total_penalty_cost:.2f}")
        print(f"  总成本: {total_cost:.2f}")
        
        return total_cost, total_travel_cost, total_penalty_cost
        
    except Exception as e:
        print(f"RH2 delta={delta_value} 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def run_comprehensive_delta_analysis():
    """运行全面的delta敏感性分析"""
    print("="*80)
    print("全面的Delta参数敏感性分析")
    print("="*80)
    
    # 测试RS5
    print("\n" + "="*50)
    print("测试 Robust SOCDA 5.py")
    print("="*50)
    
    rs5_cost_1, rs5_travel_1, rs5_penalty_1 = test_rs5_complete_with_delta(1)
    rs5_cost_2, rs5_travel_2, rs5_penalty_2 = test_rs5_complete_with_delta(2)
    
    if rs5_cost_1 is not None and rs5_cost_2 is not None:
        rs5_increase = rs5_cost_2 - rs5_cost_1
        rs5_increase_pct = (rs5_increase / rs5_cost_1) * 100 if rs5_cost_1 > 0 else 0
        print(f"\nRS5 总结:")
        print(f"  Delta=1 总成本: {rs5_cost_1:.2f}")
        print(f"  Delta=2 总成本: {rs5_cost_2:.2f}")
        print(f"  成本增加: {rs5_increase:.2f} ({rs5_increase_pct:.1f}%)")
    
    # 测试RH2
    print("\n" + "="*50)
    print("测试 Robust Hybrid 2.py")
    print("="*50)
    
    rh2_cost_1, rh2_travel_1, rh2_penalty_1 = test_rh2_complete_with_delta(1)
    rh2_cost_2, rh2_travel_2, rh2_penalty_2 = test_rh2_complete_with_delta(2)
    
    if rh2_cost_1 is not None and rh2_cost_2 is not None:
        rh2_increase = rh2_cost_2 - rh2_cost_1
        rh2_increase_pct = (rh2_increase / rh2_cost_1) * 100 if rh2_cost_1 > 0 else 0
        print(f"\nRH2 总结:")
        print(f"  Delta=1 总成本: {rh2_cost_1:.2f}")
        print(f"  Delta=2 总成本: {rh2_cost_2:.2f}")
        print(f"  成本增加: {rh2_increase:.2f} ({rh2_increase_pct:.1f}%)")
    
    # 最终对比
    print("\n" + "="*80)
    print("最终对比分析")
    print("="*80)
    
    if all(x is not None for x in [rs5_cost_1, rs5_cost_2, rh2_cost_1, rh2_cost_2]):
        print(f"{'算法':<15} {'Delta=1':<12} {'Delta=2':<12} {'增加量':<12} {'增加率':<12}")
        print("-" * 70)
        print(f"{'RS5':<15} {rs5_cost_1:<12.2f} {rs5_cost_2:<12.2f} {rs5_cost_2-rs5_cost_1:<12.2f} {((rs5_cost_2-rs5_cost_1)/rs5_cost_1)*100:<12.1f}%")
        print(f"{'RH2':<15} {rh2_cost_1:<12.2f} {rh2_cost_2:<12.2f} {rh2_cost_2-rh2_cost_1:<12.2f} {((rh2_cost_2-rh2_cost_1)/rh2_cost_1)*100:<12.1f}%")
        
        print(f"\n问题严重性评估:")
        rs5_severity = ((rs5_cost_2-rs5_cost_1)/rs5_cost_1)*100
        rh2_severity = ((rh2_cost_2-rh2_cost_1)/rh2_cost_1)*100
        
        if rs5_severity > 20:
            print(f"  RS5: 严重问题 ({rs5_severity:.1f}% 增长)")
        elif rs5_severity > 10:
            print(f"  RS5: 中等问题 ({rs5_severity:.1f}% 增长)")
        else:
            print(f"  RS5: 轻微问题 ({rs5_severity:.1f}% 增长)")
            
        if rh2_severity > 100:
            print(f"  RH2: 极严重问题 ({rh2_severity:.1f}% 增长)")
        elif rh2_severity > 50:
            print(f"  RH2: 严重问题 ({rh2_severity:.1f}% 增长)")
        elif rh2_severity > 20:
            print(f"  RH2: 中等问题 ({rh2_severity:.1f}% 增长)")
        else:
            print(f"  RH2: 轻微问题 ({rh2_severity:.1f}% 增长)")

if __name__ == "__main__":
    run_comprehensive_delta_analysis()
