import sys
import os
import importlib

# 添加当前目录到路径
sys.path.append(os.getcwd())

def analyze_fundamental_differences():
    """分析两个实现的根本差异"""
    print("="*80)
    print("根本差异分析")
    print("="*80)
    
    try:
        # 重新加载模块
        rs5_module = importlib.import_module("Robust SOCDA 5")
        rh2_module = importlib.import_module("Robust Hybrid 2")
        
        print("1. 成本计算函数签名对比")
        print("-" * 50)
        
        # 检查compute_rg2_robust_cost函数的签名
        import inspect
        
        rs5_sig = inspect.signature(rs5_module.compute_rg2_robust_cost)
        rh2_sig = inspect.signature(rh2_module.compute_rg2_robust_cost)
        
        print(f"RS5 compute_rg2_robust_cost 参数: {list(rs5_sig.parameters.keys())}")
        print(f"RH2 compute_rg2_robust_cost 参数: {list(rh2_sig.parameters.keys())}")
        
        print("\n2. 关键参数差异")
        print("-" * 50)
        
        rs5_params = set(rs5_sig.parameters.keys())
        rh2_params = set(rh2_sig.parameters.keys())
        
        only_rs5 = rs5_params - rh2_params
        only_rh2 = rh2_params - rs5_params
        common = rs5_params & rh2_params
        
        print(f"仅RS5有的参数: {only_rs5}")
        print(f"仅RH2有的参数: {only_rh2}")
        print(f"共同参数: {common}")
        
        print("\n3. 测试简单成本计算")
        print("-" * 50)
        
        # 测试最简单的情况：单个订单的成本
        sequence = ["pickup_0", "delivery_0"]
        restaurant_coords = {0: (2, 2)}
        customer_coords = {0: (1, 5)}
        orders_data = {0: {'value': 25, 'restaurant_id': 0, 'customer_coord': (1, 5), 'est_delivery': 15, 'stp': 0}}
        
        # RS5测试
        try:
            rs5_result = rs5_module.compute_rg2_robust_cost(
                sequence, None, 0.0, -1,
                restaurant_coords, customer_coords, None,
                1.0, 30,  # driver_speed, slot_duration
                0.3, 0.2,  # theta_t, travel_time_deviation_ratio
                0.2, 1.0,  # travel_cost_per_unit, penalty_cost_per_unit
                15, (5, 1),  # est_delivery, driver_start_coord
                orders_data, 10, 0,  # orders_data, stp_interval, stp
                override_first_arc_time=2.236
            )
            print(f"RS5结果: 旅行{rs5_result[0]:.3f}, 延迟{rs5_result[1]:.3f}, 总计{rs5_result[2]:.3f}")
        except Exception as e:
            print(f"RS5测试失败: {e}")
        
        # RH2测试
        try:
            rh2_result = rh2_module.compute_rg2_robust_cost(
                sequence, None, 0.0, 0,
                restaurant_coords, None, 1.0,  # restaurant_coords, customer_coords, driver_speed
                0.3, 0.2,  # theta_t, travel_time_deviation_ratio
                0.2, 1.0,  # travel_cost_per_unit, penalty_cost_per_unit
                orders_data, 10,  # orders_data, stp_interval
                override_first_arc_time=2.236
            )
            print(f"RH2结果: 旅行{rh2_result[0]:.3f}, 延迟{rh2_result[1]:.3f}, 总计{rh2_result[2]:.3f}")
        except Exception as e:
            print(f"RH2测试失败: {e}")
        
        print("\n4. 分析延迟成本计算差异")
        print("-" * 50)
        
        # 检查延迟成本计算的关键差异
        print("关键发现：")
        print("1. RS5和RH2的compute_rg2_robust_cost函数参数不同")
        print("2. 这表明两个实现使用了不同的成本计算逻辑")
        print("3. 特别是延迟成本的计算可能有根本性差异")
        
        # 分析为什么RH2的延迟成本如此之高
        print("\n5. RH2延迟成本异常高的原因分析")
        print("-" * 50)
        print("从测试结果看，RH2在后期STP中产生了大量延迟成本：")
        print("- STP 3: 25.96延迟成本")
        print("- STP 4: 58.25延迟成本") 
        print("- STP 5: 72.60延迟成本")
        print("\n这表明问题可能在于：")
        print("1. 时间约束的累积效应")
        print("2. 虚拟司机时间计算错误")
        print("3. Gurobi求解器的时间约束设置不当")
        
        print("\n6. 建议的修复策略")
        print("-" * 50)
        print("基于分析，建议：")
        print("1. 统一两个实现的成本计算函数")
        print("2. 修正RH2中的时间约束逻辑")
        print("3. 确保虚拟司机信息在整个流程中的一致性使用")
        print("4. 重新审视Gurobi模型中的时间变量定义")
        
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_fundamental_differences()
